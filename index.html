<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students - Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1>Students</h1>
        <div class="actions">
            <span class="material-icons">search</span>
            <span class="material-icons">more_vert</span>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-section">
            <a href="#" class="sidebar-item">
                <span class="material-icons">home</span>
                <span>Home</span>
            </a>
            <a href="#" class="sidebar-item active">
                <span class="material-icons">people</span>
                <span>Students</span>
            </a>
            <div class="sidebar-item" id="grades-menu">
                <span class="material-icons">grade</span>
                <span>Grades</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="grades-submenu">
                <a href="#" class="submenu-item">Grades List</a>
                <a href="#" class="submenu-item">Subjects</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <div class="actions">
                <span class="material-icons">share</span>
                <span class="material-icons">favorite_border</span>
            </div>
        </div>

        <!-- Data Table (Desktop) -->
        <div class="mdc-data-table-container">
            <div class="data-table-header">
                <div class="data-table-title">Students</div>
                <div class="data-table-controls">
                    <div class="search-container">
                        <span class="material-icons search-icon">search</span>
                        <input type="text" class="search-input" placeholder="Search students..." id="table-search">
                    </div>
                    <div class="per-page-container">
                        <span class="per-page-label">Rows per page:</span>
                        <select class="per-page-select" id="per-page-select">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="mdc-data-table" id="students-data-table">
                <div class="mdc-data-table__table-container">
                    <table class="mdc-data-table__table" id="students-table">
                        <thead>
                            <tr class="mdc-data-table__header-row">
                                <th class="mdc-data-table__header-cell sortable" data-column="name" role="columnheader" scope="col">Student</th>
                                <th class="mdc-data-table__header-cell sortable" data-column="level" role="columnheader" scope="col">Grade</th>
                                <th class="mdc-data-table__header-cell sortable" data-column="birth" role="columnheader" scope="col">Birth Date</th>
                                <th class="mdc-data-table__header-cell sortable" data-column="id" role="columnheader" scope="col">Student ID</th>
                                <th class="mdc-data-table__header-cell sortable" data-column="date" role="columnheader" scope="col">Added</th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="mdc-data-table__content" id="table-body">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="table-pagination">
                <div class="pagination-info" id="pagination-info">
                    Showing 1-10 of 8 students
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" id="prev-page" disabled>
                        <span class="material-icons">chevron_left</span>
                    </button>
                    <div class="pagination-pages" id="pagination-pages">
                        <button class="pagination-page active">1</button>
                    </div>
                    <button class="pagination-btn" id="next-page" disabled>
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Students List (Mobile) -->
        <div class="students-list">
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Alexander Thompson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: March 15, 2008 • Student ID: ST2024001
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 15</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Sophia Rodriguez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: July 22, 2007 • Student ID: ST2024002
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 14</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Marcus Johnson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: November 8, 2009 • Student ID: ST2024003
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 14</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Emma Williams</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: February 3, 2006 • Student ID: ST2024004
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 13</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Daniel Chen</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: September 12, 2008 • Student ID: ST2024005
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 13</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Isabella Garcia</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: May 18, 2007 • Student ID: ST2024006
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 12</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Ryan Martinez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: December 1, 2009 • Student ID: ST2024007
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 12</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Olivia Davis</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: April 7, 2006 • Student ID: ST2024008
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 11</div>
            </div>
        </div>
    </div>

    <!-- Bottom Sheet -->
    <div class="bottom-sheet-overlay" id="bottom-sheet-overlay">
        <div class="bottom-sheet" id="bottom-sheet">
            <div class="bottom-sheet-handle"></div>
            <div class="bottom-sheet-header">
                <div class="bottom-sheet-title">Student Options</div>
                <button class="bottom-sheet-close" id="bottom-sheet-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="bottom-sheet-content">
                <div class="student-preview" id="student-preview">
                    <div class="student-photo" id="preview-photo"></div>
                    <div class="student-info">
                        <div class="student-name" id="preview-name"></div>
                        <div class="student-details" id="preview-details"></div>
                    </div>
                </div>

                <div class="bottom-sheet-actions">
                    <div class="bottom-sheet-action" id="action-details">
                        <span class="material-icons">person</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">View Details</div>
                            <div class="bottom-sheet-action-subtitle">See complete student information</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action" id="action-edit">
                        <span class="material-icons">edit</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Edit Student</div>
                            <div class="bottom-sheet-action-subtitle">Update student information</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action" id="action-payment">
                        <span class="material-icons">payment</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Add Payment Record</div>
                            <div class="bottom-sheet-action-subtitle">Record a new payment</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action delete" id="action-delete">
                        <span class="material-icons">delete</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Delete Student</div>
                            <div class="bottom-sheet-action-subtitle">Remove student from system</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>