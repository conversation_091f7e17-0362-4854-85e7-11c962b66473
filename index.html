<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students - Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            display: flex;
            height: 100vh;
        }
        
        .app-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: linear-gradient(135deg, #6200ea, #7c4dff);
            color: white;
            height: 64px;
            display: flex;
            align-items: center;
            padding: 0 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .app-bar .material-icons {
            margin-right: 16px;
            cursor: pointer;
        }
        
        .app-bar h1 {
            font-size: 20px;
            font-weight: 500;
            flex: 1;
        }
        
        .app-bar .actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .app-bar .actions .material-icons {
            margin: 0;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
            overflow: hidden;
        }

        .app-bar .actions .material-icons:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #e0e0e0;
            margin-top: 64px;
            height: calc(100vh - 64px);
            overflow-y: auto;
            position: fixed;
            left: 0;
        }
        
        .sidebar-section {
            padding: 16px 0;
        }
        
        .sidebar-section:not(:last-child) {
            border-bottom: 1px solid #e0e0e0;
        }
        
        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #424242;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }
        
        .sidebar-item:hover {
            background-color: #f5f5f5;
        }
        
        .sidebar-item.active {
            background-color: #e8eaf6;
            color: #6200ea;
            border-right: 3px solid #6200ea;
        }
        
        .sidebar-item .material-icons {
            margin-right: 16px;
            font-size: 20px;
        }
        
        .sidebar-item span:not(.material-icons) {
            font-size: 14px;
            font-weight: 400;
            flex: 1;
        }
        
        .sidebar-item .expand-icon {
            margin-left: auto;
            margin-right: 0;
            transition: transform 0.2s;
            font-size: 18px;
        }
        
        .sidebar-item.expanded .expand-icon {
            transform: rotate(180deg);
        }
        
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: #fafafa;
        }
        
        .submenu.expanded {
            max-height: 200px;
        }
        
        .submenu-item {
            display: flex;
            align-items: center;
            padding: 10px 24px 10px 56px;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #424242;
            text-decoration: none;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .submenu-item:hover {
            background-color: #f0f0f0;
        }
        
        .submenu-item.active {
            background-color: #e8eaf6;
            color: #6200ea;
        }
        
        .main-content {
            margin-left: 280px;
            margin-top: 64px;
            padding: 24px;
            flex: 1;
            background: white;
            min-height: calc(100vh - 64px);
        }
        
        .content-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .content-header .material-icons {
            margin-right: 16px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.2s;
            position: relative;
            overflow: hidden;
        }
        
        .content-header .material-icons:hover {
            background-color: #f5f5f5;
        }
        
        .content-header .actions {
            margin-left: auto;
            display: flex;
            gap: 8px;
        }
        
        .student-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
            overflow: hidden;
        }
        
        .student-item:hover {
            background-color: #fafafa;
        }
        
        .student-item:last-child {
            border-bottom: none;
        }
        
        .student-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 16px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border: 2px solid #e0e0e0;
        }
        
        .student-info {
            flex: 1;
        }
        
        .student-name {
            font-size: 16px;
            font-weight: 500;
            color: #212121;
            margin-bottom: 4px;
        }
        
        .student-details {
            font-size: 14px;
            color: #757575;
            line-height: 1.4;
        }
        
        .student-level {
            display: inline-block;
            background: #e8eaf6;
            color: #6200ea;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
        }
        
        .student-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .edit-btn {
            padding: 8px;
            border-radius: 50%;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #757575;
            position: relative;
            overflow: hidden;
        }
        
        .edit-btn:hover {
            background-color: #f5f5f5;
            color: #6200ea;
        }
        
        .student-date {
            font-size: 12px;
            color: #9e9e9e;
            margin-left: 16px;
            white-space: nowrap;
        }
        
        /* Ripple Effect Styles */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        .ripple-dark {
            background: rgba(0, 0, 0, 0.3);
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1>Students</h1>
        <div class="actions">
            <span class="material-icons">search</span>
            <span class="material-icons">more_vert</span>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-section">
            <a href="#" class="sidebar-item">
                <span class="material-icons">home</span>
                <span>Home</span>
            </a>
            <a href="#" class="sidebar-item active">
                <span class="material-icons">people</span>
                <span>Students</span>
            </a>
            <div class="sidebar-item" id="grades-menu">
                <span class="material-icons">grade</span>
                <span>Grades</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="grades-submenu">
                <a href="#" class="submenu-item">Grades List</a>
                <a href="#" class="submenu-item">Subjects</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <div class="actions">
                <span class="material-icons">share</span>
                <span class="material-icons">favorite_border</span>
            </div>
        </div>
        
        <!-- Students List -->
        <div class="students-list">
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Alexander Thompson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: March 15, 2008 • Student ID: ST2024001
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 15</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Sophia Rodriguez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: July 22, 2007 • Student ID: ST2024002
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 14</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Marcus Johnson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: November 8, 2009 • Student ID: ST2024003
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 14</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Emma Williams</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: February 3, 2006 • Student ID: ST2024004
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 13</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Daniel Chen</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: September 12, 2008 • Student ID: ST2024005
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 13</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Isabella Garcia</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: May 18, 2007 • Student ID: ST2024006
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 12</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Ryan Martinez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: December 1, 2009 • Student ID: ST2024007
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 12</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Olivia Davis</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: April 7, 2006 • Student ID: ST2024008
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 11</div>
            </div>
        </div>
    </div>
    
    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>
    
    <script>
        // Initialize Material Design Components
        mdc.autoInit();

        // Ripple Effect Function
        function createRipple(event, element, isDark = false) {
            const circle = document.createElement('span');
            const diameter = Math.max(element.clientWidth, element.clientHeight);
            const radius = diameter / 2;

            const rect = element.getBoundingClientRect();
            circle.style.width = circle.style.height = `${diameter}px`;
            circle.style.left = `${event.clientX - rect.left - radius}px`;
            circle.style.top = `${event.clientY - rect.top - radius}px`;
            circle.classList.add('ripple');

            if (isDark) {
                circle.classList.add('ripple-dark');
            }

            const ripple = element.getElementsByClassName('ripple')[0];
            if (ripple) {
                ripple.remove();
            }

            element.appendChild(circle);

            // Remove ripple after animation
            setTimeout(() => {
                circle.remove();
            }, 600);
        }
        
        // Mobile menu toggle
        const menuBtn = document.getElementById('menu-btn');
        const sidebar = document.getElementById('sidebar');

        menuBtn.addEventListener('click', (e) => {
            createRipple(e, menuBtn);
            sidebar.classList.toggle('open');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !menuBtn.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            }
        });
        
        // Grades submenu toggle
        const gradesMenu = document.getElementById('grades-menu');
        const gradesSubmenu = document.getElementById('grades-submenu');

        gradesMenu.addEventListener('click', (e) => {
            createRipple(e, gradesMenu, true);
            gradesMenu.classList.toggle('expanded');
            gradesSubmenu.classList.toggle('expanded');
        });
        
        // Sidebar item interactions
        document.querySelectorAll('.sidebar-item:not(#grades-menu)').forEach(item => {
            item.addEventListener('click', (e) => {
                createRipple(e, item, true);
                // Remove active class from all items
                document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked item
                item.classList.add('active');
            });
        });
        
        // Submenu item interactions
        document.querySelectorAll('.submenu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                createRipple(e, item, true);
                // Remove active class from all items
                document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked submenu item
                item.classList.add('active');
            });
        });
        
        // Edit button interactions
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                createRipple(e, btn, true);
                const studentName = btn.closest('.student-item').querySelector('.student-name').textContent;
                console.log('Edit student:', studentName);
                // Here you would typically open an edit modal or navigate to edit page
            });
        });
        
        // Student item interactions
        document.querySelectorAll('.student-item').forEach(item => {
            item.addEventListener('click', (e) => {
                // Don't trigger if edit button was clicked
                if (e.target.closest('.edit-btn')) return;

                createRipple(e, item, true);
                const studentName = item.querySelector('.student-name').textContent;
                console.log('Student clicked:', studentName);
                // Here you would typically navigate to student detail page
            });
        });
        
        // Responsive sidebar handling
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('open');
            }
        });
    </script>
</body>
</html>