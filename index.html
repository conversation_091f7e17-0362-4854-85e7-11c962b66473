<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students - Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1>Students</h1>
        <div class="actions">
            <span class="material-icons">search</span>
            <span class="material-icons">more_vert</span>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-section">
            <a href="#" class="sidebar-item">
                <span class="material-icons">home</span>
                <span>Home</span>
            </a>
            <a href="#" class="sidebar-item active">
                <span class="material-icons">people</span>
                <span>Students</span>
            </a>
            <div class="sidebar-item" id="grades-menu">
                <span class="material-icons">grade</span>
                <span>Grades</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="grades-submenu">
                <a href="#" class="submenu-item">Grades List</a>
                <a href="#" class="submenu-item">Subjects</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <div class="actions">
                <span class="material-icons">share</span>
                <span class="material-icons">favorite_border</span>
            </div>
        </div>
        
        <!-- Students List -->
        <div class="students-list">
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Alexander Thompson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: March 15, 2008 • Student ID: ST2024001
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 15</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Sophia Rodriguez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: July 22, 2007 • Student ID: ST2024002
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 14</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Marcus Johnson</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: November 8, 2009 • Student ID: ST2024003
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 14</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Emma Williams</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: February 3, 2006 • Student ID: ST2024004
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 13</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Daniel Chen</div>
                    <div class="student-details">
                        <span class="student-level">Grade 10</span>
                        Born: September 12, 2008 • Student ID: ST2024005
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 13</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Isabella Garcia</div>
                    <div class="student-details">
                        <span class="student-level">Grade 11</span>
                        Born: May 18, 2007 • Student ID: ST2024006
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 12</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Ryan Martinez</div>
                    <div class="student-details">
                        <span class="student-level">Grade 9</span>
                        Born: December 1, 2009 • Student ID: ST2024007
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 12</div>
            </div>
            
            <div class="student-item">
                <div class="student-photo" style="background-image: url('https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face')"></div>
                <div class="student-info">
                    <div class="student-name">Olivia Davis</div>
                    <div class="student-details">
                        <span class="student-level">Grade 12</span>
                        Born: April 7, 2006 • Student ID: ST2024008
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn">
                        <span class="material-icons">edit</span>
                    </button>
                </div>
                <div class="student-date">Jan 11</div>
            </div>
        </div>
    </div>
    
    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>
    
    <script>
        // Initialize Material Design Components
        mdc.autoInit();

        // Ripple Effect Function
        function createRipple(event, element, isDark = false) {
            const circle = document.createElement('span');
            const diameter = Math.max(element.clientWidth, element.clientHeight);
            const radius = diameter / 2;

            const rect = element.getBoundingClientRect();
            circle.style.width = circle.style.height = `${diameter}px`;
            circle.style.left = `${event.clientX - rect.left - radius}px`;
            circle.style.top = `${event.clientY - rect.top - radius}px`;
            circle.classList.add('ripple');

            if (isDark) {
                circle.classList.add('ripple-dark');
            }

            const ripple = element.getElementsByClassName('ripple')[0];
            if (ripple) {
                ripple.remove();
            }

            element.appendChild(circle);

            // Remove ripple after animation
            setTimeout(() => {
                circle.remove();
            }, 600);
        }
        
        // Mobile menu toggle
        const menuBtn = document.getElementById('menu-btn');
        const sidebar = document.getElementById('sidebar');

        menuBtn.addEventListener('click', (e) => {
            createRipple(e, menuBtn);
            sidebar.classList.toggle('open');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !menuBtn.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            }
        });
        
        // Grades submenu toggle
        const gradesMenu = document.getElementById('grades-menu');
        const gradesSubmenu = document.getElementById('grades-submenu');

        gradesMenu.addEventListener('click', (e) => {
            createRipple(e, gradesMenu, true);
            gradesMenu.classList.toggle('expanded');
            gradesSubmenu.classList.toggle('expanded');
        });
        
        // Sidebar item interactions
        document.querySelectorAll('.sidebar-item:not(#grades-menu)').forEach(item => {
            item.addEventListener('click', (e) => {
                createRipple(e, item, true);
                // Remove active class from all items
                document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked item
                item.classList.add('active');
            });
        });
        
        // Submenu item interactions
        document.querySelectorAll('.submenu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                createRipple(e, item, true);
                // Remove active class from all items
                document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked submenu item
                item.classList.add('active');
            });
        });
        
        // Edit button interactions
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                createRipple(e, btn, true);
                const studentName = btn.closest('.student-item').querySelector('.student-name').textContent;
                console.log('Edit student:', studentName);
                // Here you would typically open an edit modal or navigate to edit page
            });
        });
        
        // Student item interactions
        document.querySelectorAll('.student-item').forEach(item => {
            item.addEventListener('click', (e) => {
                // Don't trigger if edit button was clicked
                if (e.target.closest('.edit-btn')) return;

                createRipple(e, item, true);
                const studentName = item.querySelector('.student-name').textContent;
                console.log('Student clicked:', studentName);
                // Here you would typically navigate to student detail page
            });
        });
        
        // Add ripple effects to app bar action buttons
        document.querySelectorAll('.app-bar .actions .material-icons').forEach(icon => {
            icon.addEventListener('click', (e) => {
                createRipple(e, icon);
            });
        });

        // Add ripple effects to content header icons
        document.querySelectorAll('.content-header .material-icons').forEach(icon => {
            icon.addEventListener('click', (e) => {
                createRipple(e, icon, true);
            });
        });

        // Responsive sidebar handling
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('open');
            }
        });
    </script>
</body>
</html>