/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: #f5f5f5;
    display: flex;
    height: 100vh;
}

/* App Bar Styles */
.app-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, #6200ea, #7c4dff);
    color: white;
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.app-bar .material-icons {
    margin-right: 16px;
    cursor: pointer;
}

.app-bar h1 {
    font-size: 20px;
    font-weight: 500;
    flex: 1;
}

.app-bar .actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.app-bar .actions .material-icons {
    margin: 0;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.app-bar .actions .material-icons:hover {
    background-color: rgba(255,255,255,0.1);
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e0e0e0;
    margin-top: 64px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    position: fixed;
    left: 0;
}

.sidebar-section {
    padding: 16px 0;
}

.sidebar-section:not(:last-child) {
    border-bottom: 1px solid #e0e0e0;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #424242;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.sidebar-item:hover {
    background-color: #f5f5f5;
}

.sidebar-item.active {
    background-color: #e8eaf6;
    color: #6200ea;
    border-right: 3px solid #6200ea;
}

.sidebar-item .material-icons {
    margin-right: 16px;
    font-size: 20px;
}

.sidebar-item span:not(.material-icons) {
    font-size: 14px;
    font-weight: 400;
    flex: 1;
}

.sidebar-item .expand-icon {
    margin-left: auto;
    margin-right: 0;
    transition: transform 0.2s;
    font-size: 18px;
}

.sidebar-item.expanded .expand-icon {
    transform: rotate(180deg);
}

/* Submenu Styles */
.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: #fafafa;
}

.submenu.expanded {
    max-height: 200px;
}

.submenu-item {
    display: flex;
    align-items: center;
    padding: 10px 24px 10px 56px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #424242;
    text-decoration: none;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.submenu-item:hover {
    background-color: #f0f0f0;
}

.submenu-item.active {
    background-color: #e8eaf6;
    color: #6200ea;
}

/* Main Content Styles */
.main-content {
    margin-left: 280px;
    margin-top: 64px;
    padding: 24px;
    flex: 1;
    background: white;
    min-height: calc(100vh - 64px);
}

.content-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.content-header .material-icons {
    margin-right: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.content-header .material-icons:hover {
    background-color: #f5f5f5;
}

.content-header .actions {
    margin-left: auto;
    display: flex;
    gap: 8px;
}

/* Student List Styles */
.student-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.student-item:hover {
    background-color: #fafafa;
}

.student-item:last-child {
    border-bottom: none;
}

.student-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 16px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #e0e0e0;
}

.student-info {
    flex: 1;
}

.student-name {
    font-size: 16px;
    font-weight: 500;
    color: #212121;
    margin-bottom: 4px;
}

.student-details {
    font-size: 14px;
    color: #757575;
    line-height: 1.4;
}

.student-level {
    display: inline-block;
    background: #e8eaf6;
    color: #6200ea;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
}

.student-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.edit-btn {
    padding: 8px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #757575;
    position: relative;
    overflow: hidden;
}

.edit-btn:hover {
    background-color: #f5f5f5;
    color: #6200ea;
}

.student-date {
    font-size: 12px;
    color: #9e9e9e;
    margin-left: 16px;
    white-space: nowrap;
}

/* Ripple Effect Styles */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

.ripple-dark {
    background: rgba(0, 0, 0, 0.3);
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Bottom Sheet Styles */
.bottom-sheet-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.bottom-sheet-overlay.active {
    opacity: 1;
    visibility: visible;
}

.bottom-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 2001;
    max-height: 60vh;
    overflow-y: auto;
}

.bottom-sheet.active {
    transform: translateY(0);
}

.bottom-sheet-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bottom-sheet-handle {
    width: 32px;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    margin: 8px auto 16px;
}

.bottom-sheet-title {
    font-size: 18px;
    font-weight: 500;
    color: #212121;
    flex: 1;
}

.bottom-sheet-close {
    padding: 8px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #757575;
    position: relative;
    overflow: hidden;
    transition: background-color 0.2s;
}

.bottom-sheet-close:hover {
    background-color: #f5f5f5;
}

.bottom-sheet-content {
    padding: 0 24px 24px;
}

.student-preview {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
}

.student-preview .student-photo {
    width: 48px;
    height: 48px;
    margin-right: 12px;
}

.student-preview .student-info {
    flex: 1;
}

.student-preview .student-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.student-preview .student-details {
    font-size: 14px;
    color: #757575;
}

.bottom-sheet-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.bottom-sheet-action {
    display: flex;
    align-items: center;
    padding: 16px 0;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
}

.bottom-sheet-action:hover {
    background-color: #f5f5f5;
}

.bottom-sheet-action .material-icons {
    margin-right: 16px;
    font-size: 24px;
    color: #757575;
    width: 24px;
    text-align: center;
}

.bottom-sheet-action.delete .material-icons {
    color: #f44336;
}

.bottom-sheet-action-content {
    flex: 1;
}

.bottom-sheet-action-title {
    font-size: 16px;
    font-weight: 400;
    color: #212121;
    margin-bottom: 2px;
}

.bottom-sheet-action.delete .bottom-sheet-action-title {
    color: #f44336;
}

.bottom-sheet-action-subtitle {
    font-size: 14px;
    color: #757575;
}

/* Data Table Container */
.data-table-container {
    display: none;
    width: 100%;
}

/* Table Controls */
.table-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    gap: 16px;
    flex-wrap: wrap;
}

.table-controls .mdc-text-field {
    min-width: 280px;
    flex: 1;
    max-width: 400px;
}

.per-page-container {
    display: flex;
    align-items: center;
    gap: 12px;
    white-space: nowrap;
}

.per-page-label {
    font-size: 14px;
    color: #757575;
}

.per-page-container .mdc-select {
    min-width: 80px;
}

/* MDC Data Table Full Width */
.mdc-data-table {
    width: 100%;
}

.mdc-data-table__table {
    width: 100%;
    table-layout: auto;
}

/* Sortable Header Styling */
.mdc-data-table__header-cell--sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

.mdc-data-table__header-cell--sortable:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.sort-icon {
    font-size: 16px;
    color: #bdbdbd;
    margin-left: 4px;
    vertical-align: middle;
    transition: color 0.2s;
}

.mdc-data-table__header-cell--sortable.sort-asc .sort-icon {
    color: #6200ea;
}

.mdc-data-table__header-cell--sortable.sort-desc .sort-icon {
    color: #6200ea;
}

/* Student Photo in Table */
.student-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #e0e0e0;
}

/* Table Actions */
.table-actions {
    display: flex;
    gap: 4px;
}

.table-actions .mdc-icon-button {
    width: 32px;
    height: 32px;
    padding: 4px;
}

/* Pagination */
.table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 8px;
    flex-wrap: wrap;
    gap: 16px;
}

.pagination-info {
    font-size: 14px;
    color: #757575;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-pages {
    display: flex;
    gap: 4px;
}

.pagination-page {
    min-width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #757575;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.pagination-page:hover {
    background-color: rgba(98, 0, 234, 0.04);
}

.pagination-page.active {
    background-color: #6200ea;
    color: white;
}

.pagination-page.active:hover {
    background-color: #5c00d6;
}

/* Small Input Variants */
.mdc-text-field.mdc-small-input {
    height: 40px;
}

.mdc-text-field.mdc-small-input .mdc-text-field__input {
    padding: 8px 12px;
    font-size: 14px;
}

.mdc-text-field.mdc-small-input.mdc-text-field--outlined .mdc-text-field__input {
    padding: 8px 12px;
    height: 40px;
}

.mdc-text-field.mdc-small-input.mdc-text-field--with-leading-icon .mdc-text-field__input {
    padding-left: 36px;
}

.mdc-text-field.mdc-small-input .mdc-text-field__icon {
    font-size: 18px;
}

.mdc-text-field.mdc-small-input .mdc-floating-label {
    font-size: 14px;
}

.mdc-text-field.mdc-small-input.mdc-text-field--outlined .mdc-floating-label {
    top: 50%;
    transform: translateY(-50%);
}

.mdc-text-field.mdc-small-input.mdc-text-field--outlined.mdc-text-field--label-floating .mdc-floating-label {
    top: 0;
    transform: translateY(-50%) scale(0.75);
}

.mdc-select.mdc-small-input {
    height: 40px;
}

.mdc-select.mdc-small-input .mdc-select__anchor {
    height: 40px;
    min-height: 40px;
}

.mdc-select.mdc-small-input .mdc-select__selected-text {
    font-size: 14px;
    line-height: 24px;
    padding-top: 8px;
    padding-bottom: 8px;
}

.mdc-select.mdc-small-input .mdc-select__dropdown-icon {
    width: 20px;
    height: 20px;
}

/* Filtering Component */
.filter-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #e0e0e0;
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.filter-title {
    font-size: 16px;
    font-weight: 500;
    color: #212121;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.filter-toggle:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.filter-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.filter-content.expanded {
    max-height: 300px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-size: 14px;
    font-weight: 500;
    color: #757575;
}

.filter-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
}

.filter-btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    position: relative;
    overflow: hidden;
}

.filter-btn:hover {
    background-color: #f5f5f5;
}

.filter-btn.primary {
    background-color: #6200ea;
    color: white;
    border-color: #6200ea;
}

.filter-btn.primary:hover {
    background-color: #5c00d6;
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.filter-chip {
    display: inline-flex;
    align-items: center;
    background: #e8eaf6;
    color: #6200ea;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 12px;
    gap: 4px;
}

.filter-chip .material-icons {
    font-size: 16px;
    cursor: pointer;
    border-radius: 50%;
    padding: 2px;
    transition: background-color 0.2s;
}

.filter-chip .material-icons:hover {
    background-color: rgba(98, 0, 234, 0.1);
}

/* Responsive Design */
@media (min-width: 1024px) {
    .students-list {
        display: none;
    }

    .data-table-container {
        display: block;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .bottom-sheet {
        max-height: 70vh;
    }

    /* Table Controls Mobile */
    .table-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .table-controls .mdc-text-field {
        min-width: auto;
        max-width: none;
    }

    .per-page-container {
        justify-content: space-between;
    }

    /* Table Mobile Responsiveness */
    .mdc-data-table__table-container {
        overflow-x: auto;
    }

    .mdc-data-table__header-cell,
    .mdc-data-table__cell {
        padding: 12px 8px;
        font-size: 13px;
    }

    .student-photo {
        width: 32px;
        height: 32px;
    }

    /* Pagination Mobile */
    .table-pagination {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }


}
