/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: #f5f5f5;
    display: flex;
    height: 100vh;
}

/* App Bar Styles */
.app-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, #6200ea, #7c4dff);
    color: white;
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.app-bar .material-icons {
    margin-right: 16px;
    cursor: pointer;
}

.app-bar h1 {
    font-size: 20px;
    font-weight: 500;
    flex: 1;
}

.app-bar .actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.app-bar .actions .material-icons {
    margin: 0;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.app-bar .actions .material-icons:hover {
    background-color: rgba(255,255,255,0.1);
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e0e0e0;
    margin-top: 64px;
    height: calc(100vh - 64px);
    overflow-y: auto;
    position: fixed;
    left: 0;
}

.sidebar-section {
    padding: 16px 0;
}

.sidebar-section:not(:last-child) {
    border-bottom: 1px solid #e0e0e0;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #424242;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.sidebar-item:hover {
    background-color: #f5f5f5;
}

.sidebar-item.active {
    background-color: #e8eaf6;
    color: #6200ea;
    border-right: 3px solid #6200ea;
}

.sidebar-item .material-icons {
    margin-right: 16px;
    font-size: 20px;
}

.sidebar-item span:not(.material-icons) {
    font-size: 14px;
    font-weight: 400;
    flex: 1;
}

.sidebar-item .expand-icon {
    margin-left: auto;
    margin-right: 0;
    transition: transform 0.2s;
    font-size: 18px;
}

.sidebar-item.expanded .expand-icon {
    transform: rotate(180deg);
}

/* Submenu Styles */
.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: #fafafa;
}

.submenu.expanded {
    max-height: 200px;
}

.submenu-item {
    display: flex;
    align-items: center;
    padding: 10px 24px 10px 56px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #424242;
    text-decoration: none;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.submenu-item:hover {
    background-color: #f0f0f0;
}

.submenu-item.active {
    background-color: #e8eaf6;
    color: #6200ea;
}

/* Main Content Styles */
.main-content {
    margin-left: 280px;
    margin-top: 64px;
    padding: 24px;
    flex: 1;
    background: white;
    min-height: calc(100vh - 64px);
}

.content-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.content-header .material-icons {
    margin-right: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.content-header .material-icons:hover {
    background-color: #f5f5f5;
}

.content-header .actions {
    margin-left: auto;
    display: flex;
    gap: 8px;
}

/* Student List Styles */
.student-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.student-item:hover {
    background-color: #fafafa;
}

.student-item:last-child {
    border-bottom: none;
}

.student-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 16px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #e0e0e0;
}

.student-info {
    flex: 1;
}

.student-name {
    font-size: 16px;
    font-weight: 500;
    color: #212121;
    margin-bottom: 4px;
}

.student-details {
    font-size: 14px;
    color: #757575;
    line-height: 1.4;
}

.student-level {
    display: inline-block;
    background: #e8eaf6;
    color: #6200ea;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
}

.student-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.edit-btn {
    padding: 8px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #757575;
    position: relative;
    overflow: hidden;
}

.edit-btn:hover {
    background-color: #f5f5f5;
    color: #6200ea;
}

.student-date {
    font-size: 12px;
    color: #9e9e9e;
    margin-left: 16px;
    white-space: nowrap;
}

/* Ripple Effect Styles */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

.ripple-dark {
    background: rgba(0, 0, 0, 0.3);
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Bottom Sheet Styles */
.bottom-sheet-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.bottom-sheet-overlay.active {
    opacity: 1;
    visibility: visible;
}

.bottom-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 2001;
    max-height: 60vh;
    overflow-y: auto;
}

.bottom-sheet.active {
    transform: translateY(0);
}

.bottom-sheet-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bottom-sheet-handle {
    width: 32px;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    margin: 8px auto 16px;
}

.bottom-sheet-title {
    font-size: 18px;
    font-weight: 500;
    color: #212121;
    flex: 1;
}

.bottom-sheet-close {
    padding: 8px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #757575;
    position: relative;
    overflow: hidden;
    transition: background-color 0.2s;
}

.bottom-sheet-close:hover {
    background-color: #f5f5f5;
}

.bottom-sheet-content {
    padding: 0 24px 24px;
}

.student-preview {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
}

.student-preview .student-photo {
    width: 48px;
    height: 48px;
    margin-right: 12px;
}

.student-preview .student-info {
    flex: 1;
}

.student-preview .student-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.student-preview .student-details {
    font-size: 14px;
    color: #757575;
}

.bottom-sheet-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.bottom-sheet-action {
    display: flex;
    align-items: center;
    padding: 16px 0;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
}

.bottom-sheet-action:hover {
    background-color: #f5f5f5;
}

.bottom-sheet-action .material-icons {
    margin-right: 16px;
    font-size: 24px;
    color: #757575;
    width: 24px;
    text-align: center;
}

.bottom-sheet-action.delete .material-icons {
    color: #f44336;
}

.bottom-sheet-action-content {
    flex: 1;
}

.bottom-sheet-action-title {
    font-size: 16px;
    font-weight: 400;
    color: #212121;
    margin-bottom: 2px;
}

.bottom-sheet-action.delete .bottom-sheet-action-title {
    color: #f44336;
}

.bottom-sheet-action-subtitle {
    font-size: 14px;
    color: #757575;
}

/* MDC Data Table Container */
.mdc-data-table-container {
    display: none;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Custom Header for Controls */
.data-table-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.data-table-title {
    font-size: 20px;
    font-weight: 500;
    color: #212121;
}

.data-table-controls {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;
}

/* MDC Text Field Customization */
.data-table-controls .mdc-text-field {
    width: 280px;
}

.data-table-controls .mdc-text-field--outlined .mdc-notched-outline__leading,
.data-table-controls .mdc-text-field--outlined .mdc-notched-outline__notch,
.data-table-controls .mdc-text-field--outlined .mdc-notched-outline__trailing {
    border-color: #e0e0e0;
}

.data-table-controls .mdc-text-field--focused .mdc-notched-outline__leading,
.data-table-controls .mdc-text-field--focused .mdc-notched-outline__notch,
.data-table-controls .mdc-text-field--focused .mdc-notched-outline__trailing {
    border-color: #6200ea;
    border-width: 2px;
}

.data-table-controls .mdc-text-field__icon--leading {
    color: #757575;
}

.data-table-controls .mdc-text-field--focused .mdc-text-field__icon--leading {
    color: #6200ea;
}

/* Per Page Container */
.per-page-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.per-page-label {
    font-size: 14px;
    color: #757575;
    white-space: nowrap;
}

/* MDC Select Customization */
.per-page-container .mdc-select {
    min-width: 80px;
}

.per-page-container .mdc-select .mdc-notched-outline__leading,
.per-page-container .mdc-select .mdc-notched-outline__trailing {
    border-color: #e0e0e0;
}

.per-page-container .mdc-select--focused .mdc-notched-outline__leading,
.per-page-container .mdc-select--focused .mdc-notched-outline__trailing {
    border-color: #6200ea;
    border-width: 2px;
}

.per-page-container .mdc-select__dropdown-icon {
    fill: #757575;
}

.per-page-container .mdc-select--focused .mdc-select__dropdown-icon {
    fill: #6200ea;
}

/* MDC Data Table Overrides and Extensions */
.mdc-data-table {
    border: none;
}

.mdc-data-table__table {
    width: 100%;
}

.mdc-data-table__header-cell {
    font-size: 14px;
    font-weight: 500;
    color: #757575;
    padding: 16px 24px;
    position: relative;
    cursor: pointer;
    user-select: none;
}

.mdc-data-table__header-cell:hover {
    background: #f0f0f0;
}

.mdc-data-table__header-cell.sortable::after {
    content: 'unfold_more';
    font-family: 'Material Icons';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #bdbdbd;
}

.mdc-data-table__header-cell.sort-asc::after {
    content: 'keyboard_arrow_up';
    color: #6200ea;
}

.mdc-data-table__header-cell.sort-desc::after {
    content: 'keyboard_arrow_down';
    color: #6200ea;
}

.mdc-data-table__cell {
    padding: 16px 24px;
    font-size: 14px;
    color: #212121;
}

.mdc-data-table__row:hover {
    background: #fafafa;
}

.mdc-data-table__row.mdc-data-table__row--selected {
    background: #e8eaf6;
}

/* Custom Table Content Styles */
.table-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #e0e0e0;
}

.table-student-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.table-student-details {
    display: flex;
    flex-direction: column;
}

.table-student-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.table-student-id {
    font-size: 12px;
    color: #757575;
}

.table-level-badge {
    display: inline-block;
    background: #e8eaf6;
    color: #6200ea;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.table-actions {
    display: flex;
    gap: 8px;
}

.table-action-btn {
    padding: 6px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #757575;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.table-action-btn:hover {
    background-color: #f5f5f5;
    color: #6200ea;
}

/* Custom Pagination */
.table-pagination {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.pagination-info {
    font-size: 14px;
    color: #757575;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    padding: 8px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #757575;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #f5f5f5;
    color: #6200ea;
}

.pagination-btn:disabled {
    color: #bdbdbd;
    cursor: not-allowed;
}

.pagination-pages {
    display: flex;
    gap: 4px;
}

.pagination-page {
    min-width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #757575;
    transition: background-color 0.2s;
    position: relative;
    overflow: hidden;
}

.pagination-page:hover {
    background-color: #f5f5f5;
}

.pagination-page.active {
    background-color: #6200ea;
    color: white;
}

.no-results {
    text-align: center;
    padding: 48px 24px;
    color: #757575;
}

.no-results .material-icons {
    font-size: 48px;
    margin-bottom: 16px;
    color: #bdbdbd;
}

/* Responsive Design */
@media (min-width: 1024px) {
    .students-list {
        display: none;
    }

    .mdc-data-table-container {
        display: block;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .bottom-sheet {
        max-height: 70vh;
    }

    .data-table-header {
        flex-direction: column;
        align-items: stretch;
    }

    .data-table-controls {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .data-table-controls .mdc-text-field {
        width: 100%;
    }

    .per-page-container {
        justify-content: space-between;
    }

    .data-table th,
    .data-table td {
        padding: 12px 16px;
    }

    .table-pagination {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .pagination-controls {
        justify-content: center;
    }
}
