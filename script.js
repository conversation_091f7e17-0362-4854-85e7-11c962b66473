// Initialize Material Design Components
mdc.autoInit();

// Student Data
const studentsData = [
    {
        id: 'ST2024001',
        name: '<PERSON>',
        level: 'Grade 10',
        birth: 'March 15, 2008',
        photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 15'
    },
    {
        id: 'ST2024002',
        name: '<PERSON>',
        level: 'Grade 11',
        birth: 'July 22, 2007',
        photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14'
    },
    {
        id: 'ST2024003',
        name: '<PERSON>',
        level: 'Grade 9',
        birth: 'November 8, 2009',
        photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14'
    },
    {
        id: 'ST2024004',
        name: '<PERSON>',
        level: 'Grade 12',
        birth: 'February 3, 2006',
        photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13'
    },
    {
        id: 'ST2024005',
        name: 'Daniel Chen',
        level: 'Grade 10',
        birth: 'September 12, 2008',
        photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13'
    },
    {
        id: 'ST2024006',
        name: 'Isabella Garcia',
        level: 'Grade 11',
        birth: 'May 18, 2007',
        photo: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12'
    },
    {
        id: 'ST2024007',
        name: 'Ryan Martinez',
        level: 'Grade 9',
        birth: 'December 1, 2009',
        photo: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12'
    },
    {
        id: 'ST2024008',
        name: 'Olivia Davis',
        level: 'Grade 12',
        birth: 'April 7, 2006',
        photo: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 11'
    }
];

// Data Table State
let currentPage = 1;
let perPage = 10;
let sortColumn = '';
let sortDirection = '';
let searchQuery = '';
let filteredData = [...studentsData];

// Filter State
let activeFilters = {
    grade: '',
    dateFrom: '',
    dateTo: ''
};

// MDC Component instances
let searchTextField;
let perPageSelect;
let dataTable;
let gradeFilterSelect;
let dateFromField;
let dateToField;

// Data Table Functions
function filterData() {
    filteredData = studentsData.filter(student => {
        // Search filter
        const matchesSearch = !searchQuery ||
            student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            student.level.toLowerCase().includes(searchQuery.toLowerCase()) ||
            student.id.toLowerCase().includes(searchQuery.toLowerCase());

        // Grade filter
        const matchesGrade = !activeFilters.grade || student.level === activeFilters.grade;

        // Date filter (simplified - in real app you'd parse actual dates)
        const matchesDate = true; // Placeholder for date filtering logic

        return matchesSearch && matchesGrade && matchesDate;
    });

    currentPage = 1;
    renderTable();
    updateActiveFiltersDisplay();
}

function sortData(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    filteredData.sort((a, b) => {
        let aVal = a[column];
        let bVal = b[column];

        if (column === 'birth') {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
        }

        if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    updateSortHeaders();
    renderTable();
}

function updateSortHeaders() {
    document.querySelectorAll('.mdc-data-table__header-cell--sortable').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.textContent = 'unfold_more';
        }

        if (th.dataset.column === sortColumn) {
            th.classList.add(sortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            if (icon) {
                icon.textContent = sortDirection === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
            }
        }
    });
}

function renderTable() {
    const tableBody = document.getElementById('table-body');
    const startIndex = (currentPage - 1) * perPage;
    const endIndex = startIndex + perPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    if (pageData.length === 0) {
        tableBody.innerHTML = `
            <tr class="mdc-data-table__row">
                <td class="mdc-data-table__cell" colspan="7" style="text-align: center; padding: 48px;">
                    <div style="color: #757575;">
                        <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                        No students found
                    </div>
                </td>
            </tr>
        `;
    } else {
        tableBody.innerHTML = pageData.map(student => `
            <tr class="mdc-data-table__row table-row" data-student-id="${student.id}">
                <td class="mdc-data-table__cell">
                    <div class="student-photo" style="background-image: url('${student.photo}')"></div>
                </td>
                <th class="mdc-data-table__cell" scope="row">${student.name}</th>
                <td class="mdc-data-table__cell">${student.level}</td>
                <td class="mdc-data-table__cell">${student.birth}</td>
                <td class="mdc-data-table__cell">${student.id}</td>
                <td class="mdc-data-table__cell">${student.date}</td>
                <td class="mdc-data-table__cell">
                    <div class="table-actions">
                        <button class="mdc-icon-button" title="View Details">
                            <span class="material-icons">visibility</span>
                        </button>
                        <button class="mdc-icon-button" title="Edit">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="mdc-icon-button" title="More Options">
                            <span class="material-icons">more_vert</span>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updatePagination();
    attachTableEventListeners();
}

function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / perPage);
    const startItem = filteredData.length === 0 ? 0 : (currentPage - 1) * perPage + 1;
    const endItem = Math.min(currentPage * perPage, filteredData.length);

    // Update pagination info
    document.getElementById('pagination-info').textContent =
        `Showing ${startItem}-${endItem} of ${filteredData.length} students`;

    // Update pagination buttons
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages || totalPages === 0;

    // Update page numbers
    const pagesContainer = document.getElementById('pagination-pages');
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    pagesContainer.innerHTML = '';
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `pagination-page ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.addEventListener('click', () => {
            currentPage = i;
            renderTable();
        });
        pagesContainer.appendChild(pageBtn);
    }
}

function attachTableEventListeners() {
    // Add ripple effects to table rows
    document.querySelectorAll('.mdc-data-table__row.table-row').forEach(row => {
        row.addEventListener('click', (e) => {
            // Don't trigger if action button was clicked
            if (e.target.closest('.mdc-icon-button')) return;

            createRipple(e, row, true);

            // Extract student data for bottom sheet
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);

            if (student) {
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            }
        });
    });

    // Add ripple effects to action buttons
    document.querySelectorAll('.table-actions .mdc-icon-button').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            createRipple(e, btn, true);

            const row = btn.closest('.mdc-data-table__row');
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);
            const action = btn.title.toLowerCase();

            if (action.includes('more')) {
                // Show bottom sheet for more options
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            } else {
                console.log(`${action} student:`, student.name);
            }
        });
    });
}

// Filter Functions
function updateActiveFiltersDisplay() {
    const activeFiltersContainer = document.getElementById('active-filters');
    activeFiltersContainer.innerHTML = '';

    Object.entries(activeFilters).forEach(([key, value]) => {
        if (value) {
            const chip = document.createElement('div');
            chip.className = 'filter-chip';
            chip.innerHTML = `
                <span>${key === 'grade' ? value : `${key}: ${value}`}</span>
                <span class="material-icons" onclick="removeFilter('${key}')">close</span>
            `;
            activeFiltersContainer.appendChild(chip);
        }
    });
}

function removeFilter(filterKey) {
    activeFilters[filterKey] = '';

    // Update UI controls
    if (filterKey === 'grade' && gradeFilterSelect) {
        gradeFilterSelect.value = '';
    } else if (filterKey === 'dateFrom') {
        document.getElementById('date-from').value = '';
    } else if (filterKey === 'dateTo') {
        document.getElementById('date-to').value = '';
    }

    filterData();
}

function clearAllFilters() {
    activeFilters = { grade: '', dateFrom: '', dateTo: '' };

    // Reset UI controls
    if (gradeFilterSelect) gradeFilterSelect.value = '';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';

    filterData();
}

function applyFilters() {
    // Get values from filter controls
    activeFilters.grade = gradeFilterSelect ? gradeFilterSelect.value : '';
    activeFilters.dateFrom = document.getElementById('date-from').value;
    activeFilters.dateTo = document.getElementById('date-to').value;

    filterData();
}

function initializeDataTableControls() {
    // Initialize MDC Text Field for search
    const searchTextFieldEl = document.querySelector('.table-controls .mdc-text-field');
    if (searchTextFieldEl && window.mdc && window.mdc.textField) {
        searchTextField = new mdc.textField.MDCTextField(searchTextFieldEl);

        // Add search input listener
        const searchInput = document.getElementById('table-search');
        searchInput.addEventListener('input', (e) => {
            searchQuery = e.target.value;
            filterData();
        });
    }

    // Initialize MDC Select for per-page
    const perPageSelectEl = document.querySelector('.per-page-container .mdc-select');
    if (perPageSelectEl && window.mdc && window.mdc.select) {
        perPageSelect = new mdc.select.MDCSelect(perPageSelectEl);

        // Add change listener
        perPageSelect.listen('MDCSelect:change', () => {
            perPage = parseInt(perPageSelect.value);
            currentPage = 1;
            renderTable();
        });
    }

    // Initialize filter controls
    const gradeFilterEl = document.querySelector('.filter-group .mdc-select');
    if (gradeFilterEl && window.mdc && window.mdc.select) {
        gradeFilterSelect = new mdc.select.MDCSelect(gradeFilterEl);
    }

    const dateFromEl = document.querySelector('#date-from').closest('.mdc-text-field');
    if (dateFromEl && window.mdc && window.mdc.textField) {
        dateFromField = new mdc.textField.MDCTextField(dateFromEl);
    }

    const dateToEl = document.querySelector('#date-to').closest('.mdc-text-field');
    if (dateToEl && window.mdc && window.mdc.textField) {
        dateToField = new mdc.textField.MDCTextField(dateToEl);
    }

    // Filter toggle functionality
    const filterToggle = document.getElementById('filter-toggle');
    const filterContent = document.getElementById('filter-content');

    filterToggle.addEventListener('click', (e) => {
        createRipple(e, filterToggle, true);
        filterContent.classList.toggle('expanded');
        const icon = filterToggle.querySelector('.material-icons');
        icon.textContent = filterContent.classList.contains('expanded') ? 'expand_less' : 'expand_more';
    });

    // Filter action buttons
    document.getElementById('apply-filters').addEventListener('click', (e) => {
        createRipple(e, e.target, true);
        applyFilters();
    });

    document.getElementById('clear-filters').addEventListener('click', (e) => {
        createRipple(e, e.target, true);
        clearAllFilters();
    });
}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Initialize MDC Data Table component
    const dataTableEl = document.querySelector('.mdc-data-table');
    if (dataTableEl && window.mdc && window.mdc.dataTable) {
        dataTable = new mdc.dataTable.MDCDataTable(dataTableEl);
    }

    // Initialize controls
    initializeDataTableControls();

    // Add sort listeners
    document.querySelectorAll('.mdc-data-table__header-cell--sortable').forEach(th => {
        th.addEventListener('click', (e) => {
            createRipple(e, th, true);
            sortData(th.dataset.column);
        });
    });

    // Add pagination listeners
    document.getElementById('prev-page').addEventListener('click', (e) => {
        createRipple(e, e.target, true);
        if (currentPage > 1) {
            currentPage--;
            renderTable();
        }
    });

    document.getElementById('next-page').addEventListener('click', (e) => {
        createRipple(e, e.target, true);
        const totalPages = Math.ceil(filteredData.length / perPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderTable();
        }
    });

    // Initial render
    renderTable();
});

// Ripple Effect Function
function createRipple(event, element, isDark = false) {
    const circle = document.createElement('span');
    const diameter = Math.max(element.clientWidth, element.clientHeight);
    const radius = diameter / 2;
    
    const rect = element.getBoundingClientRect();
    circle.style.width = circle.style.height = `${diameter}px`;
    circle.style.left = `${event.clientX - rect.left - radius}px`;
    circle.style.top = `${event.clientY - rect.top - radius}px`;
    circle.classList.add('ripple');
    
    if (isDark) {
        circle.classList.add('ripple-dark');
    }
    
    const ripple = element.getElementsByClassName('ripple')[0];
    if (ripple) {
        ripple.remove();
    }
    
    element.appendChild(circle);
    
    // Remove ripple after animation
    setTimeout(() => {
        circle.remove();
    }, 600);
}

// Mobile menu toggle
const menuBtn = document.getElementById('menu-btn');
const sidebar = document.getElementById('sidebar');

menuBtn.addEventListener('click', (e) => {
    createRipple(e, menuBtn);
    sidebar.classList.toggle('open');
});

// Close sidebar when clicking outside on mobile
document.addEventListener('click', (e) => {
    if (window.innerWidth <= 768) {
        if (!sidebar.contains(e.target) && !menuBtn.contains(e.target)) {
            sidebar.classList.remove('open');
        }
    }
});

// Grades submenu toggle
const gradesMenu = document.getElementById('grades-menu');
const gradesSubmenu = document.getElementById('grades-submenu');

gradesMenu.addEventListener('click', (e) => {
    createRipple(e, gradesMenu, true);
    gradesMenu.classList.toggle('expanded');
    gradesSubmenu.classList.toggle('expanded');
});

// Sidebar item interactions
document.querySelectorAll('.sidebar-item:not(#grades-menu)').forEach(item => {
    item.addEventListener('click', (e) => {
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked item
        item.classList.add('active');
    });
});

// Submenu item interactions
document.querySelectorAll('.submenu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked submenu item
        item.classList.add('active');
    });
});

// Edit button interactions
document.querySelectorAll('.edit-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, btn, true);
        const studentName = btn.closest('.student-item').querySelector('.student-name').textContent;
        console.log('Edit student:', studentName);
        // Here you would typically open an edit modal or navigate to edit page
    });
});

// Bottom Sheet Elements
const bottomSheetOverlay = document.getElementById('bottom-sheet-overlay');
const bottomSheet = document.getElementById('bottom-sheet');
const bottomSheetClose = document.getElementById('bottom-sheet-close');
const previewPhoto = document.getElementById('preview-photo');
const previewName = document.getElementById('preview-name');
const previewDetails = document.getElementById('preview-details');

// Bottom Sheet Functions
function showBottomSheet(studentData) {
    // Populate student preview
    previewPhoto.style.backgroundImage = studentData.photo;
    previewName.textContent = studentData.name;
    previewDetails.innerHTML = `<span class="student-level">${studentData.level}</span>${studentData.details}`;

    // Show bottom sheet
    bottomSheetOverlay.classList.add('active');
    bottomSheet.classList.add('active');

    // Store current student data for actions
    bottomSheet.dataset.studentId = studentData.id;
    bottomSheet.dataset.studentName = studentData.name;
}

function hideBottomSheet() {
    bottomSheetOverlay.classList.remove('active');
    bottomSheet.classList.remove('active');
}

// Bottom Sheet Event Listeners
bottomSheetClose.addEventListener('click', (e) => {
    createRipple(e, bottomSheetClose, true);
    hideBottomSheet();
});

bottomSheetOverlay.addEventListener('click', (e) => {
    if (e.target === bottomSheetOverlay) {
        hideBottomSheet();
    }
});

// Student item interactions
document.querySelectorAll('.student-item').forEach(item => {
    item.addEventListener('click', (e) => {
        // Don't trigger if edit button was clicked
        if (e.target.closest('.edit-btn')) return;

        createRipple(e, item, true);

        // Extract student data
        const studentData = {
            id: item.dataset.studentId || item.querySelector('.student-name').textContent.replace(/\s+/g, '').toLowerCase(),
            name: item.querySelector('.student-name').textContent,
            level: item.querySelector('.student-level').textContent,
            details: item.querySelector('.student-details').textContent.replace(item.querySelector('.student-level').textContent, '').trim(),
            photo: item.querySelector('.student-photo').style.backgroundImage
        };

        showBottomSheet(studentData);
    });
});

// Add ripple effects to app bar action buttons
document.querySelectorAll('.app-bar .actions .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon);
    });
});

// Add ripple effects to content header icons
document.querySelectorAll('.content-header .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon, true);
    });
});

// Bottom Sheet Action Handlers
document.getElementById('action-details').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('View details for:', studentName);
    hideBottomSheet();
    // Here you would navigate to student details page
});

document.getElementById('action-edit').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Edit student:', studentName);
    hideBottomSheet();
    // Here you would open edit modal or navigate to edit page
});

document.getElementById('action-payment').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Add payment record for:', studentName);
    hideBottomSheet();
    // Here you would open payment modal or navigate to payment page
});

document.getElementById('action-delete').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    const studentId = bottomSheet.dataset.studentId;

    // Show confirmation dialog
    if (confirm(`Are you sure you want to delete ${studentName}? This action cannot be undone.`)) {
        console.log('Delete student:', studentName, 'ID:', studentId);
        hideBottomSheet();
        // Here you would call delete API and remove from DOM
        // Example: removeStudentFromList(studentId);
    }
});

// Add ripple effects to bottom sheet actions
document.querySelectorAll('.bottom-sheet-action').forEach(action => {
    action.addEventListener('click', (e) => {
        if (!e.currentTarget.id) { // Only add ripple if not handled by specific action
            createRipple(e, action, true);
        }
    });
});

// Keyboard support for bottom sheet
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && bottomSheetOverlay.classList.contains('active')) {
        hideBottomSheet();
    }
});

// Responsive sidebar handling
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        sidebar.classList.remove('open');
    }
});
